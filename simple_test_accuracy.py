#!/usr/bin/env python3
"""
Simple Test Accuracy - Test đơn giản chỉ hiển thị kết quả và tỷ lệ chính xác
Sử dụng phân tích ngắn hạn 7 kì
"""

import time
from simple_keno_predictor import SimpleKenoPredictor
from variable_length_model import connect_db

class SimpleAccuracyTester:
    """
    Test đơn giản:
    - Dự đoán từ kì 51
    - Hiển thị kết quả ngắn gọn
    - Tính % chính xác
    """
    
    def __init__(self):
        self.predictor = SimpleKenoPredictor()
        
    def get_test_dates(self):
        """L<PERSON>y danh sách ngày cần test (từ 2025-04-01)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            query = """
                SELECT DISTINCT date 
                FROM histories_keno 
                WHERE date >= '2025-04-01'
                ORDER BY date ASC
            """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            return [row['date'] for row in rows]
            
        except Exception as e:
            return []
    
    def get_day_draws(self, test_date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """
            
            cursor.execute(query, (test_date,))
            rows = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]
            
            return rows
            
        except Exception as e:
            return []
    
    def calculate_accuracy(self, predicted_missing, actual_results):
        """Tính độ chính xác dự đoán số trượt"""
        if not predicted_missing or not actual_results:
            return 0, 0, 0
        
        # Số trượt thực tế = các số từ 1-80 không có trong actual_results
        actual_missing = set(range(1, 81)) - set(actual_results)
        predicted_set = set(predicted_missing)
        
        # Số dự đoán đúng
        correct_predictions = len(predicted_set & actual_missing)
        
        # Độ chính xác = số dự đoán đúng / tổng số dự đoán
        accuracy = (correct_predictions / len(predicted_missing)) * 100
        
        return accuracy, correct_predictions, len(predicted_missing)
    
    def test_single_day(self, test_date):
        """Test dự đoán cho một ngày"""
        print(f"\n=== Test ngày {test_date} ===")
        
        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)
        
        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None
        
        day_correct = 0
        day_total = 0
        predictions_count = 0
        
        # Load dữ liệu lịch sử cho predictor
        if not self.predictor.load_recent_data(100):
            print("Không thể load dữ liệu lịch sử")
            return None
        
        # Test từ kì 51 đến cuối ngày
        for draw_index in range(50, len(day_draws)):  # Từ kì 51 (index 50)
            draw_number = draw_index + 1
            
            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])
            
            # Dự đoán 6 số trượt
            try:
                predicted_missing = self.predictor.predict_missing_numbers(
                    day_results=input_draws,
                    num_predictions=6
                )
                
                if predicted_missing:
                    # Chờ 5s để "lấy kết quả từ DB"
                    time.sleep(5)
                    
                    # Lấy kết quả thực tế
                    actual_results = day_draws[draw_index]['results']
                    
                    # Tính độ chính xác
                    accuracy, correct, total = self.calculate_accuracy(predicted_missing, actual_results)
                    
                    day_correct += correct
                    day_total += total
                    predictions_count += 1
                    
                    print(f"Kì {draw_number:3d}: {predicted_missing} -> {correct}/{total} đúng ({accuracy:.0f}%)")
                    
            except Exception as e:
                pass
        
        # Tính độ chính xác cho cả ngày
        if day_total > 0:
            day_accuracy = (day_correct / day_total) * 100
            print(f"Tổng ngày: {day_correct}/{day_total} đúng ({day_accuracy:.1f}%) - {predictions_count} kì")
            
            return {
                'date': test_date,
                'day_correct': day_correct,
                'day_total': day_total,
                'day_accuracy': day_accuracy,
                'predictions_count': predictions_count
            }
        
        return None
    
    def test_all_days(self):
        """Test tất cả ngày từ 2025-04-01"""
        print("=== Test độ chính xác dự đoán (từ 2025-04-01) ===")
        
        test_dates = self.get_test_dates()
        
        if not test_dates:
            print("Không có ngày nào để test")
            return
        
        print(f"Sẽ test {len(test_dates)} ngày")
        
        total_correct = 0
        total_predictions = 0
        successful_days = 0
        
        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}]", end=" ")
            
            day_result = self.test_single_day(test_date)
            
            if day_result:
                total_correct += day_result['day_correct']
                total_predictions += day_result['day_total']
                successful_days += 1
        
        # Tổng kết
        if total_predictions > 0:
            overall_accuracy = (total_correct / total_predictions) * 100
            print(f"\n=== TỔNG KẾT ===")
            print(f"Tổng thể: {total_correct}/{total_predictions} đúng ({overall_accuracy:.2f}%)")
            print(f"Số ngày test: {successful_days}/{len(test_dates)}")
        else:
            print("\nKhông có kết quả test nào")

def quick_test_one_day(test_date=None):
    """Test nhanh một ngày"""
    tester = SimpleAccuracyTester()
    
    if test_date is None:
        # Lấy ngày gần nhất >= 2025-04-01
        test_dates = tester.get_test_dates()
        if test_dates:
            test_date = test_dates[-1]  # Ngày gần nhất
        else:
            print("Không có ngày nào để test")
            return
    
    result = tester.test_single_day(test_date)
    
    if result:
        print(f"\nKết quả: {result['day_correct']}/{result['day_total']} đúng ({result['day_accuracy']:.2f}%)")
    
    return result

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'quick':
            # Test nhanh một ngày
            test_date = sys.argv[2] if len(sys.argv) > 2 else None
            quick_test_one_day(test_date)
            
        elif mode == 'all':
            # Test tất cả ngày
            tester = SimpleAccuracyTester()
            tester.test_all_days()
            
        elif mode == 'check':
            # Kiểm tra dữ liệu có sẵn
            tester = SimpleAccuracyTester()
            test_dates = tester.get_test_dates()
            
            if test_dates:
                print(f"Có {len(test_dates)} ngày để test từ {test_dates[0]} đến {test_dates[-1]}")
            else:
                print("Không có dữ liệu để test")
                
        else:
            print("Sử dụng:")
            print("  python simple_test_accuracy.py quick        # Test ngày gần nhất")
            print("  python simple_test_accuracy.py quick 2025-04-01  # Test ngày cụ thể")
            print("  python simple_test_accuracy.py all          # Test tất cả ngày")
            print("  python simple_test_accuracy.py check        # Kiểm tra dữ liệu")
    else:
        # Mặc định test ngày gần nhất
        quick_test_one_day()

if __name__ == "__main__":
    main()
