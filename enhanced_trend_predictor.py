#!/usr/bin/env python3
"""
Enhanced Trend Predictor - D<PERSON> đoán ngắn hạn (7 kì) và trung hạn (30 kì)
<PERSON><PERSON><PERSON> hợp với LSTM model để dự đoán 6 số trượt hiệu quả
"""

import numpy as np
from variable_length_model import VariableLengthKenoModel, connect_db, print_header, print_success, print_info, print_warning

class EnhancedTrendPredictor:
    """
    Enhanced Predictor với:
    - Dự đoán ngắn hạn: 7 kì gần nhất
    - Dự đoán trung hạn: 30 kì gần nhất
    - Vector đặc trưng cho 7 kì và 30 kì
    - Kết hợp với LSTM model hiện tại
    """

    def __init__(self, model_path='keno_variable_length_model.h5'):
        self.model_path = model_path
        self.short_term_period = 7   # Ngắn hạn: 7 kì
        self.medium_term_period = 30 # Trung hạn: 30 kì

        # Load LSTM model
        self.lstm_model = None
        self.load_lstm_model()

        # Data storage
        self.data = None

    def load_lstm_model(self):
        """Load LSTM model hiện có"""
        try:
            import tensorflow as tf
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
            print_success("✅ Đã load LSTM model")
        except Exception as e:
            print_warning(f"⚠️ Không thể load LSTM model: {e}")
            self.lstm_model = None

    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        print_info("📥 Loading dữ liệu gần nhất...")

        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < self.medium_term_period:
                print_warning(f"Không đủ dữ liệu: {len(rows)} < {self.medium_term_period}")
                return False

            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            self.data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                self.data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })

            print_success(f"✅ Đã load {len(self.data)} kì")
            return True

        except Exception as e:
            print_warning(f"Lỗi load data: {e}")
            return False

    def create_frequency_vector(self, data_period):
        """Tạo vector tần suất xuất hiện"""
        frequency_vector = np.zeros(80)
        total_draws = len(data_period)

        if total_draws == 0:
            return frequency_vector

        for draw in data_period:
            for num in draw['numbers']:
                if 1 <= num <= 80:
                    frequency_vector[num-1] += 1

        # Chuyển thành tỷ lệ phần trăm
        frequency_vector = (frequency_vector / total_draws) * 100
        return frequency_vector

    def create_missing_vector(self, data_period):
        """Tạo vector số lần bị trượt"""
        missing_vector = np.zeros(80)
        total_draws = len(data_period)

        if total_draws == 0:
            return missing_vector

        for draw in data_period:
            appeared_numbers = set(draw['numbers'])
            for num in range(1, 81):
                if num not in appeared_numbers:
                    missing_vector[num-1] += 1

        # Chuyển thành tỷ lệ phần trăm
        missing_vector = (missing_vector / total_draws) * 100
        return missing_vector

    def analyze_short_term_trend(self):
        """Phân tích xu hướng ngắn hạn 7 kì"""
        if len(self.data) < self.short_term_period:
            print_warning(f"Không đủ dữ liệu cho phân tích {self.short_term_period} kì")
            return None

        # Lấy 7 kì gần nhất
        recent_data = self.data[-self.short_term_period:]

        # Tạo vector đặc trưng
        frequency_vector = self.create_frequency_vector(recent_data)
        missing_vector = self.create_missing_vector(recent_data)

        # Phân tích pattern xuất hiện
        number_stats = {}
        for num in range(1, 81):
            appearances = []
            for i, draw in enumerate(recent_data):
                if num in draw['numbers']:
                    appearances.append(i)

            # Tính các chỉ số
            freq_rate = frequency_vector[num-1]
            miss_rate = missing_vector[num-1]
            last_appearance = max(appearances) if appearances else -1
            gap_from_last = len(recent_data) - 1 - last_appearance if last_appearance >= 0 else len(recent_data)

            number_stats[num] = {
                'frequency_rate': freq_rate,
                'missing_rate': miss_rate,
                'appearances': len(appearances),
                'last_appearance': last_appearance,
                'gap_from_last': gap_from_last,
                'momentum': freq_rate / 7 * 100  # Momentum trong 7 kì
            }

        return {
            'period': self.short_term_period,
            'frequency_vector': frequency_vector,
            'missing_vector': missing_vector,
            'number_stats': number_stats,
            'data': recent_data
        }

    def analyze_medium_term_trend(self):
        """Phân tích xu hướng trung hạn 30 kì"""
        if len(self.data) < self.medium_term_period:
            print_warning(f"Không đủ dữ liệu cho phân tích {self.medium_term_period} kì")
            return None

        # Lấy 30 kì gần nhất
        recent_data = self.data[-self.medium_term_period:]

        # Tạo vector đặc trưng
        frequency_vector = self.create_frequency_vector(recent_data)
        missing_vector = self.create_missing_vector(recent_data)

        # Phân tích xu hướng tăng/giảm (so sánh 2 nửa)
        half_point = len(recent_data) // 2
        first_half = recent_data[:half_point]
        second_half = recent_data[half_point:]

        first_half_freq = self.create_frequency_vector(first_half)
        second_half_freq = self.create_frequency_vector(second_half)

        # Tính xu hướng cho từng số
        number_stats = {}
        for num in range(1, 81):
            trend = second_half_freq[num-1] - first_half_freq[num-1]

            # Tính độ ổn định (standard deviation)
            appearances_per_window = []
            window_size = 5  # Chia 30 kì thành 6 window
            for i in range(0, len(recent_data), window_size):
                window = recent_data[i:i+window_size]
                count = sum(1 for draw in window if num in draw['numbers'])
                appearances_per_window.append(count)

            stability = np.std(appearances_per_window) if len(appearances_per_window) > 1 else 0

            number_stats[num] = {
                'frequency_rate': frequency_vector[num-1],
                'missing_rate': missing_vector[num-1],
                'trend': trend,
                'stability': stability,
                'first_half_freq': first_half_freq[num-1],
                'second_half_freq': second_half_freq[num-1]
            }

        return {
            'period': self.medium_term_period,
            'frequency_vector': frequency_vector,
            'missing_vector': missing_vector,
            'number_stats': number_stats,
            'data': recent_data
        }

    def predict_short_term(self, num_predictions=6):
        """Dự đoán số trượt dựa trên xu hướng ngắn hạn 7 kì"""
        short_trend = self.analyze_short_term_trend()
        if not short_trend:
            return []

        # Tính điểm cho từng số dựa trên xu hướng ngắn hạn
        number_scores = {}
        for num in range(1, 81):
            stats = short_trend['number_stats'][num]

            # Điểm dựa trên:
            # 1. Tỷ lệ trượt cao (missing_rate)
            # 2. Khoảng cách từ lần xuất hiện cuối (gap_from_last)
            # 3. Momentum thấp (ít xuất hiện gần đây)

            missing_score = stats['missing_rate']  # Cao = tốt
            gap_score = min(stats['gap_from_last'] * 10, 100)  # Khoảng cách lâu = tốt
            momentum_score = max(0, 100 - stats['momentum'])  # Momentum thấp = tốt

            # Trọng số cho ngắn hạn
            total_score = (missing_score * 0.4 + gap_score * 0.4 + momentum_score * 0.2)
            number_scores[num] = total_score

        # Sắp xếp và lấy top predictions
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        predictions = [num for num, _ in sorted_numbers[:num_predictions]]

        return {
            'predictions': predictions,
            'scores': {num: score for num, score in sorted_numbers[:num_predictions]},
            'method': 'short_term',
            'period': self.short_term_period
        }

    def predict_medium_term(self, num_predictions=6):
        """Dự đoán số trượt dựa trên xu hướng trung hạn 30 kì"""
        medium_trend = self.analyze_medium_term_trend()
        if not medium_trend:
            return []

        # Tính điểm cho từng số dựa trên xu hướng trung hạn
        number_scores = {}
        for num in range(1, 81):
            stats = medium_trend['number_stats'][num]

            # Điểm dựa trên:
            # 1. Tỷ lệ trượt cao (missing_rate)
            # 2. Xu hướng giảm (trend âm = ít xuất hiện gần đây)
            # 3. Độ ổn định thấp (không xuất hiện đều)

            missing_score = stats['missing_rate']  # Cao = tốt
            trend_score = max(0, -stats['trend'] * 5)  # Xu hướng giảm = tốt
            stability_score = min(stats['stability'] * 20, 100)  # Không ổn định = tốt

            # Trọng số cho trung hạn
            total_score = (missing_score * 0.5 + trend_score * 0.3 + stability_score * 0.2)
            number_scores[num] = total_score

        # Sắp xếp và lấy top predictions
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        predictions = [num for num, _ in sorted_numbers[:num_predictions]]

        return {
            'predictions': predictions,
            'scores': {num: score for num, score in sorted_numbers[:num_predictions]},
            'method': 'medium_term',
            'period': self.medium_term_period
        }

    def create_combined_features(self):
        """Tạo vector đặc trưng kết hợp từ 7 kì và 30 kì với phân tích xu hướng chi tiết"""
        short_trend = self.analyze_short_term_trend()
        medium_trend = self.analyze_medium_term_trend()

        if not short_trend or not medium_trend:
            print_warning("Không thể tạo vector đặc trưng")
            return None

        # Vector đặc trưng kết hợp cho mỗi số
        combined_features = {}
        feature_matrix = np.zeros((80, 12))  # 12 chiều cho mỗi số

        for num in range(1, 81):
            short_stats = short_trend['number_stats'][num]
            medium_stats = medium_trend['number_stats'][num]

            # Vector 12 chiều cho mỗi số (mở rộng từ 8 chiều)
            features = [
                # Đặc trưng ngắn hạn (7 kì)
                short_stats['frequency_rate'],      # 0: Tần suất 7 kì
                short_stats['missing_rate'],        # 1: Tỷ lệ trượt 7 kì
                short_stats['gap_from_last'],       # 2: Khoảng cách từ lần cuối
                short_stats['momentum'],            # 3: Momentum 7 kì

                # Đặc trưng trung hạn (30 kì)
                medium_stats['frequency_rate'],     # 4: Tần suất 30 kì
                medium_stats['missing_rate'],       # 5: Tỷ lệ trượt 30 kì
                medium_stats['trend'],              # 6: Xu hướng 30 kì
                medium_stats['stability'],          # 7: Độ ổn định 30 kì

                # Đặc trưng kết hợp và phân tích xu hướng
                short_stats['missing_rate'] - medium_stats['missing_rate'],  # 8: Chênh lệch missing rate
                short_stats['frequency_rate'] - medium_stats['frequency_rate'],  # 9: Chênh lệch frequency
                abs(medium_stats['trend']) * medium_stats['stability'],  # 10: Chỉ số biến động
                (short_stats['gap_from_last'] / 7) * 100  # 11: Tỷ lệ khoảng cách chuẩn hóa
            ]

            combined_features[num] = np.array(features)
            feature_matrix[num-1] = features

        # Chuẩn hóa features (z-score normalization)
        normalized_matrix = self._normalize_features(feature_matrix)

        # Cập nhật lại combined_features với dữ liệu đã chuẩn hóa
        for num in range(1, 81):
            combined_features[num] = normalized_matrix[num-1]

        return {
            'features': combined_features,
            'feature_matrix': normalized_matrix,
            'short_trend': short_trend,
            'medium_trend': medium_trend,
            'feature_names': [
                'short_frequency', 'short_missing', 'gap_from_last', 'momentum',
                'medium_frequency', 'medium_missing', 'trend', 'stability',
                'missing_diff', 'frequency_diff', 'volatility_index', 'normalized_gap'
            ]
        }

    def _normalize_features(self, feature_matrix):
        """Chuẩn hóa features bằng z-score"""
        normalized = np.zeros_like(feature_matrix)

        for i in range(feature_matrix.shape[1]):
            column = feature_matrix[:, i]
            mean = np.mean(column)
            std = np.std(column)

            if std > 0:
                normalized[:, i] = (column - mean) / std
            else:
                normalized[:, i] = column

        return normalized

    def predict_lstm_only(self, day_results, num_predictions=6):
        """Dự đoán chỉ dùng LSTM model"""
        if not self.lstm_model or not self.lstm_model.model:
            print_warning("LSTM model chưa được load")
            return []

        try:
            # Dự đoán bằng LSTM
            probabilities = self.lstm_model.predict_next_draw(day_results)

            if probabilities is None:
                print_warning("LSTM không thể dự đoán")
                return []

            # Chuyển đổi probabilities thành missing numbers
            # Số có xác suất thấp = có khả năng trượt cao
            number_probs = [(i+1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

            predictions = [num for num, _ in number_probs[:num_predictions]]

            return {
                'predictions': predictions,
                'probabilities': {num: prob for num, prob in number_probs[:num_predictions]},
                'method': 'lstm_only'
            }

        except Exception as e:
            print_warning(f"Lỗi LSTM prediction: {e}")
            return []

    def analyze_trend_patterns(self):
        """Phân tích pattern xu hướng chi tiết để tối ưu dự đoán"""
        print_header("PHÂN TÍCH PATTERN XU HƯỚNG CHI TIẾT")

        if len(self.data) < self.medium_term_period:
            print_warning("Không đủ dữ liệu để phân tích pattern")
            return None

        # Phân tích pattern xuất hiện theo chu kỳ
        cycle_patterns = {}
        for num in range(1, 81):
            appearances = []
            for i, draw in enumerate(self.data[-self.medium_term_period:]):
                if num in draw['numbers']:
                    appearances.append(i)

            # Tính khoảng cách giữa các lần xuất hiện
            gaps = []
            if len(appearances) > 1:
                for i in range(1, len(appearances)):
                    gaps.append(appearances[i] - appearances[i-1])

            # Phân tích chu kỳ
            avg_gap = np.mean(gaps) if gaps else 30
            gap_std = np.std(gaps) if len(gaps) > 1 else 0
            last_gap = len(self.data[-self.medium_term_period:]) - 1 - max(appearances) if appearances else 30

            cycle_patterns[num] = {
                'avg_gap': avg_gap,
                'gap_std': gap_std,
                'last_gap': last_gap,
                'regularity': 1 / (gap_std + 1),  # Độ đều đặn
                'overdue_factor': max(0, last_gap - avg_gap) / avg_gap if avg_gap > 0 else 0
            }

        print_success("✅ Hoàn thành phân tích pattern xu hướng")
        return cycle_patterns

    def ensemble_prediction(self, day_results=None, num_predictions=6):
        """
        Kết hợp LSTM + ngắn hạn + trung hạn + pattern analysis để dự đoán 6 số trượt

        Trọng số được tối ưu:
        - LSTM: 35% (model đã train)
        - Trung hạn (30 kì): 30% (xu hướng ổn định)
        - Ngắn hạn (7 kì): 20% (xu hướng gần đây)
        - Pattern Analysis: 15% (phân tích chu kỳ)
        """
        print_header("ENSEMBLE PREDICTION - KẾT HỢP TẤT CẢ PHƯƠNG PHÁP NÂNG CAO")

        # Load dữ liệu nếu chưa có
        if not self.data:
            if not self.load_recent_data(100):
                print_warning("Không thể load dữ liệu")
                return None

        # 1. Dự đoán LSTM (nếu có day_results và model)
        lstm_result = []
        if self.lstm_model and day_results and len(day_results) >= 50:
            print_info("1️⃣ LSTM Prediction...")
            lstm_result = self.predict_lstm_only(day_results, num_predictions * 2)
        else:
            if not self.lstm_model:
                print_info("1️⃣ LSTM Prediction: Bỏ qua (model không load được)")
            elif not day_results or len(day_results) < 50:
                print_info("1️⃣ LSTM Prediction: Bỏ qua (không đủ dữ liệu ngày)")
            else:
                print_info("1️⃣ LSTM Prediction: Bỏ qua")

        # 2. Dự đoán ngắn hạn
        print_info("2️⃣ Short-term Prediction...")
        short_result = self.predict_short_term(num_predictions * 2)

        # 3. Dự đoán trung hạn
        print_info("3️⃣ Medium-term Prediction...")
        medium_result = self.predict_medium_term(num_predictions * 2)

        # 4. Tạo vector đặc trưng kết hợp
        print_info("4️⃣ Creating Combined Features...")
        combined_features = self.create_combined_features()

        # 5. Phân tích pattern xu hướng
        print_info("5️⃣ Analyzing Trend Patterns...")
        trend_patterns = self.analyze_trend_patterns()

        # 6. Ensemble scoring với trọng số tối ưu
        print_info("6️⃣ Advanced Ensemble Scoring...")
        final_scores = {}

        # Trọng số được tối ưu (điều chỉnh nếu không có LSTM)
        if lstm_result and 'predictions' in lstm_result:
            weights = {
                'lstm': 0.35,
                'medium': 0.30,
                'short': 0.20,
                'pattern': 0.15
            }
        else:
            # Không có LSTM, phân bổ lại trọng số
            weights = {
                'lstm': 0.0,
                'medium': 0.45,  # Tăng trọng số trung hạn
                'short': 0.35,   # Tăng trọng số ngắn hạn
                'pattern': 0.20  # Tăng trọng số pattern
            }

        # Tính điểm ensemble cho từng số
        for num in range(1, 81):
            total_score = 0

            # LSTM score
            if lstm_result and 'predictions' in lstm_result:
                if num in lstm_result['predictions']:
                    lstm_score = 100 - lstm_result['predictions'].index(num) * 8
                else:
                    lstm_score = 0
                total_score += lstm_score * weights['lstm']

            # Medium-term score
            if medium_result and 'predictions' in medium_result:
                if num in medium_result['predictions']:
                    medium_score = 100 - medium_result['predictions'].index(num) * 8
                else:
                    medium_score = 0
                total_score += medium_score * weights['medium']

            # Short-term score
            if short_result and 'predictions' in short_result:
                if num in short_result['predictions']:
                    short_score = 100 - short_result['predictions'].index(num) * 8
                else:
                    short_score = 0
                total_score += short_score * weights['short']

            # Pattern analysis score
            if trend_patterns and num in trend_patterns:
                pattern = trend_patterns[num]
                # Điểm cao cho số quá hạn và có chu kỳ đều đặn
                pattern_score = (pattern['overdue_factor'] * 50 + pattern['regularity'] * 30)
                total_score += pattern_score * weights['pattern']

            # Bonus từ combined features (nâng cao)
            if combined_features and num in combined_features['features']:
                features = combined_features['features'][num]
                # Bonus cho số có missing rate cao, trend giảm và biến động lớn
                feature_bonus = 0
                feature_bonus += features[1] * 0.05  # short missing rate
                feature_bonus += features[5] * 0.05  # medium missing rate
                feature_bonus += abs(features[8]) * 0.03  # missing rate difference
                if features[6] < 0:  # trend âm
                    feature_bonus += abs(features[6]) * 0.02
                feature_bonus += features[10] * 0.02  # volatility index

                total_score += feature_bonus

            final_scores[num] = total_score

        # Sắp xếp và lấy top predictions
        sorted_predictions = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        final_predictions = [num for num, _ in sorted_predictions[:num_predictions]]

        # Kết quả chi tiết
        result = {
            'final_predictions': final_predictions,
            'final_scores': {num: score for num, score in sorted_predictions[:num_predictions]},
            'component_results': {
                'lstm': lstm_result,
                'short_term': short_result,
                'medium_term': medium_result,
                'combined_features': combined_features,
                'trend_patterns': trend_patterns
            },
            'weights': weights,
            'method': 'ensemble_advanced'
        }

        # Hiển thị kết quả chi tiết
        print_success("🎯 KẾT QUẢ ENSEMBLE PREDICTION NÂNG CAO")
        print_info(f"📊 6 số trượt dự đoán: {final_predictions}")
        print_info(f"🔢 Điểm số: {[f'{score:.1f}' for _, score in sorted_predictions[:num_predictions]]}")

        if lstm_result and 'predictions' in lstm_result:
            print_info(f"🤖 LSTM ({weights['lstm']*100:.0f}%): {lstm_result['predictions'][:6]}")
        else:
            print_info("🤖 LSTM: Không khả dụng")
        if medium_result and 'predictions' in medium_result:
            print_info(f"📈 Trung hạn 30 kì ({weights['medium']*100:.0f}%): {medium_result['predictions'][:6]}")
        if short_result and 'predictions' in short_result:
            print_info(f"⚡ Ngắn hạn 7 kì ({weights['short']*100:.0f}%): {short_result['predictions'][:6]}")

        print_info("📋 Vector đặc trưng: 12 chiều (ngắn hạn + trung hạn + xu hướng kết hợp)")
        print_info("🔄 Pattern Analysis: Phân tích chu kỳ và độ quá hạn")

        return result

    def predict_missing_numbers(self, day_results=None, num_predictions=6, method='ensemble'):
        """
        Phương thức chính để dự đoán số trượt

        Parameters:
        - day_results: Dữ liệu các kì trong ngày (cho LSTM)
        - num_predictions: Số lượng số trượt cần dự đoán
        - method: 'ensemble', 'lstm', 'short', 'medium'
        """
        print_header(f"DỰ ĐOÁN {num_predictions} SỐ TRƯỢT - PHƯƠNG PHÁP: {method.upper()}")

        # Load dữ liệu nếu chưa có
        if not self.data:
            if not self.load_recent_data(100):
                print_warning("Không thể load dữ liệu")
                return None

        if method == 'ensemble':
            return self.ensemble_prediction(day_results, num_predictions)
        elif method == 'lstm':
            if day_results and len(day_results) >= 50:
                return self.predict_lstm_only(day_results, num_predictions)
            else:
                print_warning("LSTM cần ít nhất 50 kì trong ngày")
                return None
        elif method == 'short':
            return self.predict_short_term(num_predictions)
        elif method == 'medium':
            return self.predict_medium_term(num_predictions)
        else:
            print_warning(f"Phương pháp không hỗ trợ: {method}")
            return None


