#!/usr/bin/env python3
"""
Simple Keno Predictor - Chỉ sử dụng phân tích ngắn hạn 7 kì
Tập trung vào kết quả dự đoán và tỷ lệ chính xác
"""

import numpy as np
from variable_length_model import connect_db

class SimpleKenoPredictor:
    """
    Predictor đơn giản chỉ sử dụng:
    - <PERSON>ân tích ngắn hạn 7 kì gần nhất
    - D<PERSON> đoán 6 số trượt
    - Không có logs phức tạp
    """
    
    def __init__(self):
        self.short_term_period = 7
        self.data = None
        
    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """
            
            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            if len(rows) < self.short_term_period:
                return False
            
            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            self.data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                self.data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })
            
            return True
            
        except Exception as e:
            return False
    
    def create_frequency_vector(self, data_period):
        """Tạo vector tần suất xuất hiện"""
        frequency_vector = np.zeros(80)
        total_draws = len(data_period)
        
        if total_draws == 0:
            return frequency_vector
        
        for draw in data_period:
            for num in draw['numbers']:
                if 1 <= num <= 80:
                    frequency_vector[num-1] += 1
        
        # Chuyển thành tỷ lệ phần trăm
        frequency_vector = (frequency_vector / total_draws) * 100
        return frequency_vector
    
    def create_missing_vector(self, data_period):
        """Tạo vector số lần bị trượt"""
        missing_vector = np.zeros(80)
        total_draws = len(data_period)
        
        if total_draws == 0:
            return missing_vector
        
        for draw in data_period:
            appeared_numbers = set(draw['numbers'])
            for num in range(1, 81):
                if num not in appeared_numbers:
                    missing_vector[num-1] += 1
        
        # Chuyển thành tỷ lệ phần trăm
        missing_vector = (missing_vector / total_draws) * 100
        return missing_vector
    
    def analyze_short_term_trend(self):
        """Phân tích xu hướng ngắn hạn 7 kì"""
        if len(self.data) < self.short_term_period:
            return None
        
        # Lấy 7 kì gần nhất
        recent_data = self.data[-self.short_term_period:]
        
        # Tạo vector đặc trưng
        frequency_vector = self.create_frequency_vector(recent_data)
        missing_vector = self.create_missing_vector(recent_data)
        
        # Phân tích pattern xuất hiện
        number_stats = {}
        for num in range(1, 81):
            appearances = []
            for i, draw in enumerate(recent_data):
                if num in draw['numbers']:
                    appearances.append(i)
            
            # Tính các chỉ số
            freq_rate = frequency_vector[num-1]
            miss_rate = missing_vector[num-1]
            last_appearance = max(appearances) if appearances else -1
            gap_from_last = len(recent_data) - 1 - last_appearance if last_appearance >= 0 else len(recent_data)
            
            number_stats[num] = {
                'frequency_rate': freq_rate,
                'missing_rate': miss_rate,
                'appearances': len(appearances),
                'last_appearance': last_appearance,
                'gap_from_last': gap_from_last,
                'momentum': freq_rate / 7 * 100  # Momentum trong 7 kì
            }
        
        return {
            'period': self.short_term_period,
            'frequency_vector': frequency_vector,
            'missing_vector': missing_vector,
            'number_stats': number_stats,
            'data': recent_data
        }
    
    def predict_missing_numbers(self, day_results=None, num_predictions=6):
        """
        Dự đoán 6 số trượt dựa trên xu hướng ngắn hạn 7 kì
        
        Returns:
        List 6 số dự đoán trượt
        """
        # Load dữ liệu nếu chưa có
        if not self.data:
            if not self.load_recent_data(100):
                return []
        
        # Phân tích xu hướng ngắn hạn
        short_trend = self.analyze_short_term_trend()
        if not short_trend:
            return []
        
        # Tính điểm cho từng số dựa trên xu hướng ngắn hạn
        number_scores = {}
        for num in range(1, 81):
            stats = short_trend['number_stats'][num]
            
            # Điểm dựa trên:
            # 1. Tỷ lệ trượt cao (missing_rate)
            # 2. Khoảng cách từ lần xuất hiện cuối (gap_from_last)
            # 3. Momentum thấp (ít xuất hiện gần đây)
            
            missing_score = stats['missing_rate']  # Cao = tốt
            gap_score = min(stats['gap_from_last'] * 10, 100)  # Khoảng cách lâu = tốt
            momentum_score = max(0, 100 - stats['momentum'])  # Momentum thấp = tốt
            
            # Trọng số cho ngắn hạn
            total_score = (missing_score * 0.4 + gap_score * 0.4 + momentum_score * 0.2)
            number_scores[num] = total_score
        
        # Sắp xếp và lấy top predictions
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        predictions = [num for num, _ in sorted_numbers[:num_predictions]]
        
        return predictions

def predict_6_missing_numbers():
    """Dự đoán nhanh 6 số trượt"""
    predictor = SimpleKenoPredictor()
    predictions = predictor.predict_missing_numbers(num_predictions=6)
    
    if predictions:
        return predictions
    else:
        return []

if __name__ == "__main__":
    # Test nhanh
    predictions = predict_6_missing_numbers()
    if predictions:
        print(f"6 số dự đoán trượt: {predictions}")
    else:
        print("Không thể dự đoán")
