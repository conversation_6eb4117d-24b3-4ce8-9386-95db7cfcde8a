#!/usr/bin/env python3
"""
Enhanced Keno Predictor - <PERSON><PERSON><PERSON> hợp LSTM Model + <PERSON><PERSON> tích ngắn hạn 7 kì
Core functions:
- <PERSON>ân tích xu hướng ngắn hạn 7 kì
- Tạo vector đặc trưng
- Kết hợp LSTM model và phân tích xu hướng
- Test với dữ liệu từ 2025-04-01, dự đoán từ kì 51
"""

import numpy as np
import time
from datetime import datetime
from variable_length_model import VariableLengthKenoModel, connect_db

class EnhancedKenoPredictor:
    """
    Enhanced Predictor với core functions:
    - <PERSON>ân tích xu hướng ngắn hạn 7 kì
    - Tạo vector đặc trưng
    - Kết hợp LSTM model + phân tích xu hướng
    - Test với dữ liệu từ 2025-04-01
    """

    def __init__(self):
        self.short_term_period = 7
        self.data = None
        self.lstm_model = None
        self.model_path = "keno_variable_length_model.h5"
        self.load_lstm_model()

    def load_lstm_model(self):
        """Load LSTM model"""
        try:
            import tensorflow as tf
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
        except Exception as e:
            self.lstm_model = None

    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < self.short_term_period:
                return False

            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            self.data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                self.data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })

            return True

        except Exception as e:
            return False

    def create_frequency_vector(self, data_period):
        """Tạo vector tần suất xuất hiện"""
        frequency_vector = np.zeros(80)
        total_draws = len(data_period)

        if total_draws == 0:
            return frequency_vector

        for draw in data_period:
            for num in draw['numbers']:
                if 1 <= num <= 80:
                    frequency_vector[num-1] += 1

        # Chuyển thành tỷ lệ phần trăm
        frequency_vector = (frequency_vector / total_draws) * 100
        return frequency_vector

    def create_missing_vector(self, data_period):
        """Tạo vector số lần bị trượt"""
        missing_vector = np.zeros(80)
        total_draws = len(data_period)

        if total_draws == 0:
            return missing_vector

        for draw in data_period:
            appeared_numbers = set(draw['numbers'])
            for num in range(1, 81):
                if num not in appeared_numbers:
                    missing_vector[num-1] += 1

        # Chuyển thành tỷ lệ phần trăm
        missing_vector = (missing_vector / total_draws) * 100
        return missing_vector

    def analyze_short_term_trend(self):
        """Phân tích xu hướng ngắn hạn 7 kì"""
        if len(self.data) < self.short_term_period:
            return None

        # Lấy 7 kì gần nhất
        recent_data = self.data[-self.short_term_period:]

        # Tạo vector đặc trưng
        frequency_vector = self.create_frequency_vector(recent_data)
        missing_vector = self.create_missing_vector(recent_data)

        # Phân tích pattern xuất hiện
        number_stats = {}
        for num in range(1, 81):
            appearances = []
            for i, draw in enumerate(recent_data):
                if num in draw['numbers']:
                    appearances.append(i)

            # Tính các chỉ số
            freq_rate = frequency_vector[num-1]
            miss_rate = missing_vector[num-1]
            last_appearance = max(appearances) if appearances else -1
            gap_from_last = len(recent_data) - 1 - last_appearance if last_appearance >= 0 else len(recent_data)

            number_stats[num] = {
                'frequency_rate': freq_rate,
                'missing_rate': miss_rate,
                'appearances': len(appearances),
                'last_appearance': last_appearance,
                'gap_from_last': gap_from_last,
                'momentum': freq_rate / 7 * 100  # Momentum trong 7 kì
            }

        return {
            'period': self.short_term_period,
            'frequency_vector': frequency_vector,
            'missing_vector': missing_vector,
            'number_stats': number_stats,
            'data': recent_data
        }

    def create_feature_vector(self):
        """Tạo vector đặc trưng cho 7 kì gần nhất"""
        short_trend = self.analyze_short_term_trend()
        if not short_trend:
            return None

        # Tạo vector đặc trưng 7 chiều cho mỗi số
        feature_vectors = {}

        for num in range(1, 81):
            stats = short_trend['number_stats'][num]

            # Vector 7 chiều cho mỗi số
            features = [
                stats['frequency_rate'],     # 0: Tần suất xuất hiện
                stats['missing_rate'],       # 1: Tỷ lệ trượt
                stats['gap_from_last'],      # 2: Khoảng cách từ lần cuối
                stats['momentum'],           # 3: Momentum
                stats['appearances'],        # 4: Số lần xuất hiện
                len(short_trend['data']),    # 5: Tổng số kì phân tích
                stats['last_appearance']     # 6: Vị trí lần xuất hiện cuối
            ]

            feature_vectors[num] = np.array(features)

        return {
            'vectors': feature_vectors,
            'short_trend': short_trend,
            'feature_names': [
                'frequency_rate', 'missing_rate', 'gap_from_last', 'momentum',
                'appearances', 'total_draws', 'last_appearance'
            ]
        }

    def predict_lstm_only(self, day_results, num_predictions=6):
        """Dự đoán bằng LSTM model"""
        if not self.lstm_model or not day_results or len(day_results) < 50:
            return []

        try:
            # Dự đoán bằng LSTM
            probabilities = self.lstm_model.predict_next_draw(day_results)

            if probabilities is None:
                return []

            # Chuyển đổi probabilities thành missing numbers
            # Số có xác suất thấp = có khả năng trượt cao
            number_probs = [(i+1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

            predictions = [num for num, _ in number_probs[:num_predictions]]
            return predictions

        except Exception as e:
            return []

    def ensemble_prediction(self, day_results=None, num_predictions=6):
        """
        Core function: Kết hợp LSTM model + phân tích ngắn hạn 7 kì
        """
        # Load dữ liệu nếu chưa có
        if not self.data:
            if not self.load_recent_data(100):
                return []

        # 1. Dự đoán LSTM (nếu có day_results và model)
        lstm_predictions = []
        if self.lstm_model and day_results and len(day_results) >= 50:
            lstm_predictions = self.predict_lstm_only(day_results, num_predictions * 2)

        # 2. Phân tích xu hướng ngắn hạn
        short_trend = self.analyze_short_term_trend()
        if not short_trend:
            return lstm_predictions[:num_predictions] if lstm_predictions else []

        # 3. Tạo vector đặc trưng
        feature_data = self.create_feature_vector()
        if not feature_data:
            return lstm_predictions[:num_predictions] if lstm_predictions else []

        # 4. Ensemble scoring - kết hợp LSTM + xu hướng ngắn hạn
        final_scores = {}

        # Trọng số
        lstm_weight = 0.6 if lstm_predictions else 0.0
        trend_weight = 0.4 if lstm_predictions else 1.0

        for num in range(1, 81):
            total_score = 0

            # LSTM score
            if lstm_predictions and num in lstm_predictions:
                lstm_score = 100 - lstm_predictions.index(num) * 5
                total_score += lstm_score * lstm_weight

            # Trend analysis score
            stats = short_trend['number_stats'][num]
            missing_score = stats['missing_rate']
            gap_score = min(stats['gap_from_last'] * 10, 100)
            momentum_score = max(0, 100 - stats['momentum'])

            trend_score = (missing_score * 0.4 + gap_score * 0.4 + momentum_score * 0.2)
            total_score += trend_score * trend_weight

            # Bonus từ vector đặc trưng
            if feature_data and num in feature_data['vectors']:
                features = feature_data['vectors'][num]
                feature_bonus = features[1] * 0.1  # missing_rate bonus
                total_score += feature_bonus

            final_scores[num] = total_score

        # Sắp xếp và lấy top predictions
        sorted_predictions = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        final_predictions = [num for num, _ in sorted_predictions[:num_predictions]]

        return final_predictions

    def predict_missing_numbers(self, day_results=None, num_predictions=6):
        """
        Main prediction function - sử dụng ensemble prediction
        """
        return self.ensemble_prediction(day_results, num_predictions)

    def get_test_dates(self):
        """Lấy danh sách ngày cần test (từ 2025-04-01)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT DISTINCT date
                FROM histories_keno
                WHERE date >= '2025-04-01'
                ORDER BY date ASC
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            return [row['date'] for row in rows]

        except Exception as e:
            return []

    def get_day_draws(self, test_date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (test_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            return []

    def calculate_accuracy(self, predicted_missing, actual_results):
        """Tính độ chính xác dự đoán số trượt"""
        if not predicted_missing or not actual_results:
            return 0, 0, 0

        # Số trượt thực tế = các số từ 1-80 không có trong actual_results
        actual_missing = set(range(1, 81)) - set(actual_results)
        predicted_set = set(predicted_missing)

        # Số dự đoán đúng
        correct_predictions = len(predicted_set & actual_missing)

        # Độ chính xác = số dự đoán đúng / tổng số dự đoán
        accuracy = (correct_predictions / len(predicted_missing)) * 100

        return accuracy, correct_predictions, len(predicted_missing)

    def test_single_day(self, test_date):
        """Test dự đoán cho một ngày từ kì 51"""
        print(f"\n=== Test ngày {test_date} ===")

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        day_correct = 0
        day_total = 0
        predictions_count = 0

        # Load dữ liệu lịch sử cho predictor
        if not self.load_recent_data(100):
            print("Không thể load dữ liệu lịch sử")
            return None

        # Test từ kì 51 đến cuối ngày (21:44:00)
        for draw_index in range(50, len(day_draws)):  # Từ kì 51 (index 50)
            draw_number = draw_index + 1

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 6 số trượt bằng ensemble prediction
            try:
                predicted_missing = self.ensemble_prediction(
                    day_results=input_draws,
                    num_predictions=6
                )

                if predicted_missing:
                    # Chờ 5s để lấy kết quả từ DB
                    time.sleep(5)

                    # Lấy kết quả thực tế
                    actual_results = day_draws[draw_index]['results']

                    # Tính độ chính xác
                    accuracy, correct, total = self.calculate_accuracy(predicted_missing, actual_results)

                    day_correct += correct
                    day_total += total
                    predictions_count += 1

                    print(f"Kì {draw_number:3d}: {predicted_missing} -> {correct}/{total} đúng ({accuracy:.0f}%)")

            except Exception as e:
                pass

        # Tính độ chính xác cho cả ngày
        if day_total > 0:
            day_accuracy = (day_correct / day_total) * 100
            print(f"Tổng ngày: {day_correct}/{day_total} đúng ({day_accuracy:.1f}%) - {predictions_count} kì")

            return {
                'date': test_date,
                'day_correct': day_correct,
                'day_total': day_total,
                'day_accuracy': day_accuracy,
                'predictions_count': predictions_count
            }

        return None

    def test_all_days(self):
        """Test tất cả ngày từ 2025-04-01"""
        print("=== Test độ chính xác dự đoán (từ 2025-04-01) ===")
        print("Sử dụng: LSTM Model + Phân tích ngắn hạn 7 kì + Vector đặc trưng")

        test_dates = self.get_test_dates()

        if not test_dates:
            print("Không có ngày nào để test")
            return

        print(f"Sẽ test {len(test_dates)} ngày")

        total_correct = 0
        total_predictions = 0
        successful_days = 0

        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}]", end=" ")

            day_result = self.test_single_day(test_date)

            if day_result:
                total_correct += day_result['day_correct']
                total_predictions += day_result['day_total']
                successful_days += 1

        # Tổng kết
        if total_predictions > 0:
            overall_accuracy = (total_correct / total_predictions) * 100
            print(f"\n=== TỔNG KẾT ===")
            print(f"Tổng thể: {total_correct}/{total_predictions} đúng ({overall_accuracy:.2f}%)")
            print(f"Số ngày test: {successful_days}/{len(test_dates)}")
            print(f"Phương pháp: LSTM (60%) + Xu hướng ngắn hạn 7 kì (40%)")
        else:
            print("\nKhông có kết quả test nào")

def predict_6_missing_numbers():
    """Dự đoán nhanh 6 số trượt"""
    predictor = EnhancedKenoPredictor()
    predictions = predictor.predict_missing_numbers(num_predictions=6)

    if predictions:
        return predictions
    else:
        return []

def test_with_data_from_2025_04_01():
    """Test với dữ liệu từ 2025-04-01, dự đoán từ kì 51"""
    predictor = EnhancedKenoPredictor()
    predictor.test_all_days()

def main():
    """Main function"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'test':
            # Test với dữ liệu từ 2025-04-01
            test_with_data_from_2025_04_01()
        elif mode == 'predict':
            # Dự đoán nhanh
            predictions = predict_6_missing_numbers()
            if predictions:
                print(f"6 số dự đoán trượt: {predictions}")
            else:
                print("Không thể dự đoán")
        else:
            print("Sử dụng:")
            print("  python simple_keno_predictor.py predict  # Dự đoán 6 số trượt")
            print("  python simple_keno_predictor.py test     # Test với dữ liệu từ 2025-04-01")
    else:
        # Mặc định dự đoán
        predictions = predict_6_missing_numbers()
        if predictions:
            print(f"6 số dự đoán trượt: {predictions}")
        else:
            print("Không thể dự đoán")

if __name__ == "__main__":
    main()
