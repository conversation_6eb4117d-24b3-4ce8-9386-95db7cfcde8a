#!/usr/bin/env python3
"""
Test Prediction Accuracy - <PERSON><PERSON><PERSON> tra độ chính xác dự đoán
Test với dữ liệu từ ngày 2025-04-01 trở đi, dự đoán từ kì 51 đến cuối ngày
"""

import time
from datetime import datetime, timedelta
from enhanced_trend_predictor import EnhancedTrendPredictor
from variable_length_model import connect_db, print_header, print_success, print_info, print_warning

class PredictionAccuracyTester:
    """
    Test độ chính xác dự đoán:
    - D<PERSON> đo<PERSON> từ kì 51 trong ngày
    - Chờ 5s để lấy kết quả từ DB
    - Tính % chính xác cho từng ngày và tổng thể
    """

    def __init__(self):
        self.predictor = EnhancedTrendPredictor()
        self.test_results = []
        self.total_predictions = 0
        self.total_correct = 0

    def get_test_dates(self):
        """<PERSON><PERSON><PERSON> danh sách ngày cần test (từ 2025-04-01)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT DISTINCT date
                FROM histories_keno
                WHERE date >= '2025-04-01'
                ORDER BY date ASC
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            dates = [row['date'] for row in rows]
            print_info(f"📅 Tìm thấy {len(dates)} ngày để test từ 2025-04-01")

            return dates

        except Exception as e:
            print_warning(f"Lỗi lấy danh sách ngày: {e}")
            return []

    def get_day_draws(self, test_date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (test_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            print_warning(f"Lỗi lấy dữ liệu ngày {test_date}: {e}")
            return []

    def get_draw_result(self, test_date, draw_index):
        """Lấy kết quả của kì cụ thể (draw_index bắt đầu từ 0)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
                LIMIT %s, 1
            """

            cursor.execute(query, (test_date, draw_index))
            row = cursor.fetchone()

            cursor.close()
            conn.close()

            if row:
                row['results'] = [int(n) for n in row['results'].split(',')]
                return row
            return None

        except Exception as e:
            print_warning(f"Lỗi lấy kết quả kì {draw_index + 1}: {e}")
            return None

    def calculate_accuracy(self, predicted_missing, actual_results):
        """Tính độ chính xác dự đoán số trượt"""
        if not predicted_missing or not actual_results:
            return 0

        # Số trượt thực tế = các số từ 1-80 không có trong actual_results
        actual_missing = set(range(1, 81)) - set(actual_results)
        predicted_set = set(predicted_missing)

        # Số dự đoán đúng
        correct_predictions = len(predicted_set & actual_missing)

        # Độ chính xác = số dự đoán đúng / tổng số dự đoán
        accuracy = (correct_predictions / len(predicted_missing)) * 100

        return accuracy, correct_predictions, len(predicted_missing)

    def test_single_day(self, test_date):
        """Test dự đoán cho một ngày"""
        print_header(f"TEST NGÀY {test_date}")

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print_warning(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        print_info(f"📊 Ngày có {len(day_draws)} kì, test từ kì 51")

        day_results = {
            'date': test_date,
            'total_draws': len(day_draws),
            'predictions': [],
            'day_accuracy': 0,
            'day_correct': 0,
            'day_total': 0
        }

        # Load dữ liệu lịch sử cho predictor (không bao gồm ngày test)
        if not self.predictor.load_recent_data(100):
            print_warning("Không thể load dữ liệu lịch sử")
            return None

        # Test từ kì 51 đến cuối ngày
        for draw_index in range(50, len(day_draws)):  # Từ kì 51 (index 50)
            draw_number = draw_index + 1

            print_info(f"🔄 Dự đoán kì {draw_number}...")

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 6 số trượt
            try:
                result = self.predictor.ensemble_prediction(
                    day_results=input_draws,
                    num_predictions=6
                )

                if result and 'final_predictions' in result:
                    predicted_missing = result['final_predictions']

                    # Chờ 5s để "lấy kết quả từ DB"
                    print_info(f"   ⏳ Chờ 5s để lấy kết quả kì {draw_number}...")
                    time.sleep(5)

                    # Lấy kết quả thực tế
                    actual_draw = day_draws[draw_index]
                    actual_results = actual_draw['results']

                    # Tính độ chính xác
                    accuracy, correct, total = self.calculate_accuracy(predicted_missing, actual_results)

                    prediction_result = {
                        'draw_number': draw_number,
                        'time': actual_draw['time'],
                        'predicted_missing': predicted_missing,
                        'actual_results': actual_results,
                        'accuracy': accuracy,
                        'correct': correct,
                        'total': total
                    }

                    day_results['predictions'].append(prediction_result)
                    day_results['day_correct'] += correct
                    day_results['day_total'] += total

                    print_success(f"   ✅ Kì {draw_number}: {correct}/{total} đúng ({accuracy:.1f}%)")
                    print_info(f"      Dự đoán: {predicted_missing}")
                    print_info(f"      Kết quả: {actual_results}")

                else:
                    print_warning(f"   ❌ Không thể dự đoán kì {draw_number}")

            except Exception as e:
                print_warning(f"   ❌ Lỗi dự đoán kì {draw_number}: {e}")

        # Tính độ chính xác cho cả ngày
        if day_results['day_total'] > 0:
            day_results['day_accuracy'] = (day_results['day_correct'] / day_results['day_total']) * 100

            print_header(f"📊 KẾT QUẢ NGÀY {test_date}")
            print_success(f"✅ Tổng: {day_results['day_correct']}/{day_results['day_total']} đúng ({day_results['day_accuracy']:.1f}%)")
            print_info(f"📈 Số kì test: {len(day_results['predictions'])}")

        return day_results

    def run_full_test(self):
        """Chạy test cho tất cả ngày từ 2025-04-01"""
        print_header("TEST ĐỘ CHÍNH XÁC DỰ ĐOÁN - TỪ 2025-04-01")

        test_dates = self.get_test_dates()

        if not test_dates:
            print_warning("Không có ngày nào để test")
            return

        print_info(f"🎯 Sẽ test {len(test_dates)} ngày")
        print_info("📋 Quy trình: Dự đoán từ kì 51 → Chờ 5s → Lấy kết quả → Tính % chính xác")
        print()

        for i, test_date in enumerate(test_dates, 1):
            print_info(f"📅 Tiến độ: {i}/{len(test_dates)} ngày")

            day_result = self.test_single_day(test_date)

            if day_result:
                self.test_results.append(day_result)
                self.total_correct += day_result['day_correct']
                self.total_predictions += day_result['day_total']

            print()

        # Tổng kết
        self.print_final_summary()

    def print_final_summary(self):
        """In tổng kết cuối cùng"""
        print_header("🎉 TỔNG KẾT ĐỘ CHÍNH XÁC DỰ ĐOÁN")

        if not self.test_results:
            print_warning("Không có kết quả test nào")
            return

        # Tính tổng thể
        overall_accuracy = (self.total_correct / self.total_predictions) * 100 if self.total_predictions > 0 else 0

        print_success(f"📊 TỔNG THỂ: {self.total_correct}/{self.total_predictions} đúng ({overall_accuracy:.2f}%)")
        print_info(f"📅 Số ngày test: {len(self.test_results)}")
        print_info(f"🎯 Tổng số dự đoán: {self.total_predictions}")
        print()

        # Chi tiết từng ngày
        print_info("📋 CHI TIẾT TỪNG NGÀY:")
        for result in self.test_results:
            print(f"   {result['date']}: {result['day_correct']:3d}/{result['day_total']:3d} ({result['day_accuracy']:5.1f}%) - {len(result['predictions'])} kì")

        # Thống kê
        accuracies = [r['day_accuracy'] for r in self.test_results if r['day_total'] > 0]
        if accuracies:
            avg_daily_accuracy = sum(accuracies) / len(accuracies)
            max_accuracy = max(accuracies)
            min_accuracy = min(accuracies)

            print()
            print_info("📈 THỐNG KÊ:")
            print(f"   • Độ chính xác trung bình/ngày: {avg_daily_accuracy:.2f}%")
            print(f"   • Ngày tốt nhất: {max_accuracy:.2f}%")
            print(f"   • Ngày thấp nhất: {min_accuracy:.2f}%")

        # Phân tích theo thời gian trong ngày
        self.analyze_time_patterns()

        # Lưu kết quả
        self.save_results()

    def analyze_time_patterns(self):
        """Phân tích pattern theo thời gian trong ngày"""
        print_header("📊 PHÂN TÍCH PATTERN THEO THỜI GIAN")

        # Tổng hợp theo giờ
        hour_stats = {}

        for day_result in self.test_results:
            for pred in day_result['predictions']:
                time_str = pred['time']
                hour = int(time_str.split(':')[0])

                if hour not in hour_stats:
                    hour_stats[hour] = {'correct': 0, 'total': 0}

                hour_stats[hour]['correct'] += pred['correct']
                hour_stats[hour]['total'] += pred['total']

        # Hiển thị thống kê theo giờ
        print_info("🕐 THỐNG KÊ THEO GIỜ:")
        for hour in sorted(hour_stats.keys()):
            stats = hour_stats[hour]
            if stats['total'] > 0:
                accuracy = (stats['correct'] / stats['total']) * 100
                print(f"   {hour:2d}h: {stats['correct']:3d}/{stats['total']:3d} ({accuracy:5.1f}%)")

        # Tìm giờ tốt nhất và tệ nhất
        hour_accuracies = {}
        for hour, stats in hour_stats.items():
            if stats['total'] >= 10:  # Chỉ tính giờ có ít nhất 10 dự đoán
                hour_accuracies[hour] = (stats['correct'] / stats['total']) * 100

        if hour_accuracies:
            best_hour = max(hour_accuracies, key=hour_accuracies.get)
            worst_hour = min(hour_accuracies, key=hour_accuracies.get)

            print()
            print_info("⭐ PHÂN TÍCH:")
            print(f"   • Giờ tốt nhất: {best_hour}h ({hour_accuracies[best_hour]:.1f}%)")
            print(f"   • Giờ thấp nhất: {worst_hour}h ({hour_accuracies[worst_hour]:.1f}%)")

    def save_results(self):
        """Lưu kết quả test vào file"""
        try:
            import json
            from datetime import datetime

            # Chuẩn bị dữ liệu để lưu
            save_data = {
                'test_info': {
                    'test_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'total_days': len(self.test_results),
                    'total_predictions': self.total_predictions,
                    'total_correct': self.total_correct,
                    'overall_accuracy': (self.total_correct / self.total_predictions) * 100 if self.total_predictions > 0 else 0
                },
                'daily_results': []
            }

            # Thêm kết quả từng ngày (chỉ lưu thông tin tóm tắt)
            for result in self.test_results:
                daily_summary = {
                    'date': str(result['date']),
                    'total_draws': result['total_draws'],
                    'predictions_count': len(result['predictions']),
                    'day_correct': result['day_correct'],
                    'day_total': result['day_total'],
                    'day_accuracy': result['day_accuracy']
                }
                save_data['daily_results'].append(daily_summary)

            # Lưu file
            filename = f"prediction_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            print_success(f"💾 Đã lưu kết quả test: {filename}")

        except Exception as e:
            print_warning(f"Không thể lưu kết quả: {e}")

def quick_test_single_day(test_date=None):
    """Test nhanh cho một ngày cụ thể"""
    print_header("TEST NHANH CHO MỘT NGÀY")

    tester = PredictionAccuracyTester()

    if test_date is None:
        # Lấy ngày gần nhất >= 2025-04-01
        test_dates = tester.get_test_dates()
        if test_dates:
            test_date = test_dates[-1]  # Ngày gần nhất
        else:
            print_warning("Không có ngày nào để test")
            return

    print_info(f"🎯 Test ngày: {test_date}")

    result = tester.test_single_day(test_date)

    if result:
        print_header("📊 KẾT QUẢ TEST NGÀY")
        print_success(f"✅ {result['day_correct']}/{result['day_total']} đúng ({result['day_accuracy']:.2f}%)")
        print_info(f"📈 Số kì test: {len(result['predictions'])}")

        # Hiển thị 5 kì đầu và 5 kì cuối
        predictions = result['predictions']
        if len(predictions) > 10:
            print_info("🔍 Mẫu kết quả (5 đầu + 5 cuối):")
            for pred in predictions[:5]:
                print(f"   Kì {pred['draw_number']:3d}: {pred['correct']}/{pred['total']} ({pred['accuracy']:4.1f}%)")
            print("   ...")
            for pred in predictions[-5:]:
                print(f"   Kì {pred['draw_number']:3d}: {pred['correct']}/{pred['total']} ({pred['accuracy']:4.1f}%)")
        else:
            print_info("🔍 Tất cả kết quả:")
            for pred in predictions:
                print(f"   Kì {pred['draw_number']:3d}: {pred['correct']}/{pred['total']} ({pred['accuracy']:4.1f}%)")

    return result

def main():
    """Main function"""
    import sys

    print_header("KIỂM TRA ĐỘ CHÍNH XÁC DỰ ĐOÁN ENHANCED TREND PREDICTOR")
    print_info("🎯 Test với dữ liệu từ ngày 2025-04-01")
    print_info("📋 Dự đoán từ kì 51, chờ 5s, tính % chính xác")
    print()

    # Kiểm tra tham số dòng lệnh
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'quick':
            # Test nhanh một ngày
            test_date = sys.argv[2] if len(sys.argv) > 2 else None
            quick_test_single_day(test_date)

        elif mode == 'full':
            # Test đầy đủ tất cả ngày
            tester = PredictionAccuracyTester()
            tester.run_full_test()

        elif mode == 'check':
            # Kiểm tra dữ liệu có sẵn
            tester = PredictionAccuracyTester()
            test_dates = tester.get_test_dates()

            if test_dates:
                print_success(f"✅ Có {len(test_dates)} ngày để test:")
                for i, date in enumerate(test_dates, 1):
                    print(f"   {i:2d}. {date}")
                    if i >= 10:  # Chỉ hiển thị 10 ngày đầu
                        print(f"   ... và {len(test_dates) - 10} ngày khác")
                        break
            else:
                print_warning("❌ Không có dữ liệu để test")

        else:
            print_warning(f"Không hiểu mode: {mode}")
            print_usage()
    else:
        # Mặc định chạy test đầy đủ
        print_info("🚀 Chạy test đầy đủ (có thể mất nhiều thời gian)")
        print_info("💡 Sử dụng 'python test_prediction_accuracy.py quick' để test nhanh")
        print()

        tester = PredictionAccuracyTester()
        tester.run_full_test()

def print_usage():
    """In hướng dẫn sử dụng"""
    print_info("📖 CÁCH SỬ DỤNG:")
    print("   python test_prediction_accuracy.py                    # Test đầy đủ tất cả ngày")
    print("   python test_prediction_accuracy.py quick             # Test nhanh ngày gần nhất")
    print("   python test_prediction_accuracy.py quick 2025-04-01  # Test ngày cụ thể")
    print("   python test_prediction_accuracy.py full              # Test đầy đủ tất cả ngày")
    print("   python test_prediction_accuracy.py check             # Kiểm tra dữ liệu có sẵn")

if __name__ == "__main__":
    main()
