# Keno Labeled Model Training Guide

## 📋 Tổng quan

Hệ thống training model mới dựa trên labeled dataset để tối ưu hóa tỷ lệ dự đoán ≥5/6 số đúng.

## 🎯 Mục tiêu

- **Chính:** Tăng tỷ lệ dự đoán ≥5/6 số đúng
- **Phụ:** Tạo model thông minh biết khi nào nên dùng features b<PERSON> sung

## 📁 Files

| File | Mô tả |
|------|-------|
| `train_labeled_model.py` | Train model từ labeled dataset |
| `labeled_model_predictor.py` | Predictor sử dụng labeled model |
| `run_training_pipeline.py` | Script chạy toàn bộ pipeline |

## 🚀 Cách sử dụng

### 1. Cài đặt requirements

```bash
pip install scikit-learn joblib pandas
```

### 2. Chạy toàn bộ pipeline

```bash
python run_training_pipeline.py full
```

### 3. Hoặc chạy từng bước

```bash
# Bước 1: Tạo dataset
python simple_keno_predictor.py dataset

# Bước 2: Train model
python train_labeled_model.py

# Bước 3: Test model
python labeled_model_predictor.py test
```

## 📊 Labeled Dataset

### Cấu trúc nhãn:
- **Very Good:** 6/6 đúng
- **Good:** 5/6 đúng  
- **Normal:** 4/6 đúng
- **Bad:** ≤3/6 đúng

### Features:
- **Number stats:** 80 số x 6 features (frequency, missing_rate, gap, momentum, appearances, excluded)
- **Context:** day_results_count, excluded_count, period, flags

## 🤖 Models được train

1. **Random Forest** - Tốt cho feature importance
2. **Gradient Boosting** - Tốt cho accuracy  
3. **Logistic Regression** - Baseline

Model tốt nhất (≥5/6 recall cao nhất) được lưu với version mới.

## 📈 Logic dự đoán

```python
if predicted_label in ['Very Good', 'Good']:
    # Sử dụng ensemble prediction với full features
    return predict_with_features()
elif predicted_label == 'Normal':
    # Kết hợp 70% LSTM + 30% ensemble
    return combined_prediction()
else:  # Bad
    # Chỉ dùng LSTM
    return predict_lstm_only()
```

## 🧪 Testing

### Test model mới:
```bash
python labeled_model_predictor.py test 2025-04-01
```

### So sánh với LSTM:
```bash
# LSTM only
python simple_keno_predictor.py testlstm

# Labeled model
python labeled_model_predictor.py test
```

### Xem thông tin models:
```bash
python labeled_model_predictor.py info
```

## 📁 File outputs

- `keno_labeled_dataset_full.json` - Dataset có nhãn
- `keno_labeled_model_v1.pkl` - Model version 1
- `keno_labeled_model_v2.pkl` - Model version 2
- ...

## 🔄 Workflow

1. **Tạo dataset:** Test với features → Gán nhãn theo kết quả
2. **Train models:** RF, GB, LR → Chọn tốt nhất
3. **Predict:** Model quyết định strategy dựa trên context
4. **Evaluate:** So sánh tỷ lệ ≥5/6 với baseline

## 💡 Tips

- Model mới không ghi đè model cũ
- Có thể so sánh nhiều versions
- Pipeline tự động chọn model tốt nhất
- Fallback về LSTM nếu labeled model fail

## 🎯 Kết quả mong đợi

- Tăng tỷ lệ ≥5/6 từ ~60% lên ~70%+
- Model thông minh hơn trong việc chọn strategy
- Ổn định hơn qua các ngày khác nhau
